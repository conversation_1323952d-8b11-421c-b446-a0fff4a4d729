<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tectonic Plate Map Generator</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #222;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: Arial, sans-serif;
        }
        
        .container {
            text-align: center;
        }
        
        canvas {
            border: 2px solid #555;
            background: white;
            cursor: pointer;
        }
        
        .controls {
            margin-top: 20px;
        }
        
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        button:hover {
            background: #45a049;
        }
        
        .info {
            color: #ccc;
            margin-top: 10px;
            font-size: 14px;
            max-width: 600px;
        }
        
        .stats {
            color: #fff;
            margin-top: 15px;
            font-size: 16px;
        }
        
        .land-plate {
            color: #8B4513;
        }
        
        .water-plate {
            color: #4169E1;
        }
        
        .warning {
            color: #ff6b6b;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="color: #fff; margin-bottom: 20px;">Tectonic Plate Map Generator</h1>
        <canvas id="plateCanvas" width="960" height="540"></canvas>
        <div class="controls">
            <button onclick="generatePlates()">Generate New Plates</button>
            <button onclick="resetToDefault()">Reset to Default</button>
        </div>
        <div class="stats" id="stats"></div>
        <div class="info">
            <p>Click on any plate to toggle between land and water.</p>
            <p>Water plates must be between 60-80% of total plates (24-32 out of 40).</p>
            <p>This map uses seamless wrapping - it tiles perfectly when repeated!</p>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('plateCanvas');
        const ctx = canvas.getContext('2d');
        const width = canvas.width;
        const height = canvas.height;
        
        const TOTAL_PLATES = 40;
        const MIN_WATER_PERCENT = 60;
        const MAX_WATER_PERCENT = 80;
        const MIN_WATER_PLATES = Math.ceil(TOTAL_PLATES * MIN_WATER_PERCENT / 100);
        const MAX_WATER_PLATES = Math.floor(TOTAL_PLATES * MAX_WATER_PERCENT / 100);
        
        let plates = [];
        let plateMap = new Array(width * height);
        
        // Colors for land and water plates
        const LAND_COLOR = '#8B4513';
        const WATER_COLOR = '#4169E1';
        const BOUNDARY_COLOR = '#000000';
        
        class Plate {
            constructor(x, y, isLand = false) {
                this.x = x;
                this.y = y;
                this.isLand = isLand;
                this.id = plates.length;
            }
            
            getColor() {
                return this.isLand ? LAND_COLOR : WATER_COLOR;
            }
        }
        
        function distance(x1, y1, x2, y2) {
            return Math.sqrt((x1 - x2) ** 2 + (y1 - y2) ** 2);
        }
        
        function findClosestPlate(x, y) {
            let minDist = Infinity;
            let closestPlate = plates[0];
            
            // Check original plates and their toroidal copies
            for (let plate of plates) {
                // Original position
                let dist = distance(x, y, plate.x, plate.y);
                if (dist < minDist) {
                    minDist = dist;
                    closestPlate = plate;
                }
                
                // Toroidal wrapping - check 8 surrounding copies
                const offsets = [
                    [-width, -height], [0, -height], [width, -height],
                    [-width, 0],                     [width, 0],
                    [-width, height],  [0, height],  [width, height]
                ];
                
                for (let [dx, dy] of offsets) {
                    dist = distance(x, y, plate.x + dx, plate.y + dy);
                    if (dist < minDist) {
                        minDist = dist;
                        closestPlate = plate;
                    }
                }
            }
            
            return closestPlate;
        }
        
        function generateRandomPlates() {
            plates = [];
            for (let i = 0; i < TOTAL_PLATES; i++) {
                plates.push(new Plate(
                    Math.random() * width,
                    Math.random() * height
                ));
            }

            // Apply Lloyd's relaxation to make plates more uniform
            relaxPlates(3); // 3 iterations for good balance of uniformity vs randomness
        }

        function relaxPlates(iterations) {
            for (let iter = 0; iter < iterations; iter++) {
                // Calculate centroids for each plate
                const centroids = new Array(TOTAL_PLATES);
                const counts = new Array(TOTAL_PLATES);

                // Initialize arrays
                for (let i = 0; i < TOTAL_PLATES; i++) {
                    centroids[i] = { x: 0, y: 0 };
                    counts[i] = 0;
                }

                // Sample points across the canvas to find centroids
                const sampleStep = 2; // Sample every 2 pixels for performance
                for (let y = 0; y < height; y += sampleStep) {
                    for (let x = 0; x < width; x += sampleStep) {
                        const closestPlate = findClosestPlate(x, y);
                        const plateIndex = plates.indexOf(closestPlate);

                        centroids[plateIndex].x += x;
                        centroids[plateIndex].y += y;
                        counts[plateIndex]++;
                    }
                }

                // Move plates toward their centroids
                for (let i = 0; i < TOTAL_PLATES; i++) {
                    if (counts[i] > 0) {
                        const newX = centroids[i].x / counts[i];
                        const newY = centroids[i].y / counts[i];

                        // Move plate toward centroid (with some damping to prevent oscillation)
                        const dampingFactor = 0.8;
                        plates[i].x += (newX - plates[i].x) * dampingFactor;
                        plates[i].y += (newY - plates[i].y) * dampingFactor;

                        // Ensure plates stay within bounds
                        plates[i].x = Math.max(0, Math.min(width - 1, plates[i].x));
                        plates[i].y = Math.max(0, Math.min(height - 1, plates[i].y));
                    }
                }
            }
        }
        
        function createClusteredLandPlates() {
            // Reset all plates to water
            plates.forEach(plate => plate.isLand = false);
            
            // Create 2-3 continent clusters
            const numContinents = Math.floor(Math.random() * 2) + 2; // 2 or 3
            const platesPerContinent = Math.floor(10 / numContinents);
            let remainingLandPlates = 10;
            
            for (let continent = 0; continent < numContinents && remainingLandPlates > 0; continent++) {
                // Pick a random starting plate for this continent
                const startPlate = plates[Math.floor(Math.random() * plates.length)];
                startPlate.isLand = true;
                remainingLandPlates--;
                
                // Find nearby plates to form a cluster
                const continentPlates = [startPlate];
                const platesToAssign = continent === numContinents - 1 ? 
                    remainingLandPlates : 
                    Math.min(platesPerContinent - 1, remainingLandPlates);
                
                for (let i = 0; i < platesToAssign; i++) {
                    let closestWaterPlate = null;
                    let minDist = Infinity;
                    
                    // Find the closest water plate to any land plate in this continent
                    for (let landPlate of continentPlates) {
                        for (let plate of plates) {
                            if (!plate.isLand) {
                                const dist = distance(landPlate.x, landPlate.y, plate.x, plate.y);
                                if (dist < minDist) {
                                    minDist = dist;
                                    closestWaterPlate = plate;
                                }
                            }
                        }
                    }
                    
                    if (closestWaterPlate) {
                        closestWaterPlate.isLand = true;
                        continentPlates.push(closestWaterPlate);
                        remainingLandPlates--;
                    }
                }
            }
        }
        
        function generateVoronoiMap() {
            // Clear the plate map
            plateMap.fill(null);
            
            // Generate Voronoi diagram
            for (let y = 0; y < height; y++) {
                for (let x = 0; x < width; x++) {
                    const closestPlate = findClosestPlate(x, y);
                    plateMap[y * width + x] = closestPlate;
                }
            }
        }
        
        function drawPlates() {
            const imageData = ctx.createImageData(width, height);
            const data = imageData.data;
            
            // Fill with plate colors
            for (let y = 0; y < height; y++) {
                for (let x = 0; x < width; x++) {
                    const plate = plateMap[y * width + x];
                    const pixelIndex = (y * width + x) * 4;
                    
                    const color = plate.getColor();
                    const rgb = hexToRgb(color);
                    
                    data[pixelIndex] = rgb.r;     // R
                    data[pixelIndex + 1] = rgb.g; // G
                    data[pixelIndex + 2] = rgb.b; // B
                    data[pixelIndex + 3] = 255;   // A
                }
            }
            
            ctx.putImageData(imageData, 0, 0);
            
            // Draw plate boundaries
            drawBoundaries();
            
            // Draw plate centers
            ctx.fillStyle = 'black';
            for (let plate of plates) {
                ctx.beginPath();
                ctx.arc(plate.x, plate.y, 3, 0, 2 * Math.PI);
                ctx.fill();
            }
        }
        
        function drawBoundaries() {
            ctx.strokeStyle = BOUNDARY_COLOR;
            ctx.lineWidth = 1;
            
            for (let y = 0; y < height - 1; y++) {
                for (let x = 0; x < width - 1; x++) {
                    const currentPlate = plateMap[y * width + x];
                    const rightPlate = plateMap[y * width + (x + 1)];
                    const bottomPlate = plateMap[(y + 1) * width + x];
                    
                    // Draw vertical boundary
                    if (currentPlate !== rightPlate) {
                        ctx.beginPath();
                        ctx.moveTo(x + 1, y);
                        ctx.lineTo(x + 1, y + 1);
                        ctx.stroke();
                    }
                    
                    // Draw horizontal boundary
                    if (currentPlate !== bottomPlate) {
                        ctx.beginPath();
                        ctx.moveTo(x, y + 1);
                        ctx.lineTo(x + 1, y + 1);
                        ctx.stroke();
                    }
                }
            }
        }
        
        function hexToRgb(hex) {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? {
                r: parseInt(result[1], 16),
                g: parseInt(result[2], 16),
                b: parseInt(result[3], 16)
            } : null;
        }
        
        function updateStats() {
            const landPlates = plates.filter(p => p.isLand).length;
            const waterPlates = plates.filter(p => !p.isLand).length;
            const waterPercent = Math.round((waterPlates / TOTAL_PLATES) * 100);
            
            const statsDiv = document.getElementById('stats');
            const isValidRatio = waterPlates >= MIN_WATER_PLATES && waterPlates <= MAX_WATER_PLATES;
            
            statsDiv.innerHTML = `
                <span class="land-plate">Land Plates: ${landPlates}</span> | 
                <span class="water-plate">Water Plates: ${waterPlates} (${waterPercent}%)</span>
                ${!isValidRatio ? '<br><span class="warning">⚠️ Water percentage must be between 60-80%</span>' : ''}
            `;
        }
        
        function generatePlates() {
            generateRandomPlates();
            createClusteredLandPlates();
            generateVoronoiMap();
            drawPlates();
            updateStats();
        }
        
        function resetToDefault() {
            generatePlates();
        }
        
        // Handle canvas clicks
        canvas.addEventListener('click', function(event) {
            const rect = canvas.getBoundingClientRect();
            const x = Math.floor(event.clientX - rect.left);
            const y = Math.floor(event.clientY - rect.top);
            
            if (x >= 0 && x < width && y >= 0 && y < height) {
                const clickedPlate = plateMap[y * width + x];
                
                if (clickedPlate) {
                    const currentWaterPlates = plates.filter(p => !p.isLand).length;
                    
                    // Check if toggle is allowed
                    if (clickedPlate.isLand) {
                        // Converting land to water - check if we won't exceed max water
                        if (currentWaterPlates < MAX_WATER_PLATES) {
                            clickedPlate.isLand = false;
                        }
                    } else {
                        // Converting water to land - check if we won't go below min water
                        if (currentWaterPlates > MIN_WATER_PLATES) {
                            clickedPlate.isLand = true;
                        }
                    }
                    
                    drawPlates();
                    updateStats();
                }
            }
        });
        
        // Initialize
        generatePlates();
    </script>
</body>
</html>
